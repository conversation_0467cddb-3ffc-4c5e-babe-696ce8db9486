import { Doctor, Facility, Service } from "../types";

export const doctors: Doctor[] = [
  {
    id: "1",
    name: "Dr. <PERSON>",
    specialty: "Cardiology",
    image: "https://images.pexels.com/photos/5214959/pexels-photo-5214959.jpeg?auto=compress&cs=tinysrgb&w=800",
    description: "Dr<PERSON> is a board-certified cardiologist with over 15 years of experience in treating heart conditions.",
    education: "MD from Johns Hopkins University",
    experience: "15+ years"
  },
  {
    id: "2",
    name: "Dr. <PERSON>",
    specialty: "Neurology",
    image: "https://images.pexels.com/photos/5452293/pexels-photo-5452293.jpeg?auto=compress&cs=tinysrgb&w=800",
    description: "Dr<PERSON> specializes in neurological disorders and has pioneered several treatment methods.",
    education: "MD from Stanford University",
    experience: "12+ years"
  },
  {
    id: "3",
    name: "Dr. <PERSON>",
    specialty: "Pediatrics",
    image: "https://images.pexels.com/photos/5407206/pexels-photo-5407206.jpeg?auto=compress&cs=tinysrgb&w=800",
    description: "Dr<PERSON> is passionate about children's health and preventive care for young patients.",
    education: "MD from UCLA Medical School",
    experience: "10+ years"
  },
  {
    id: "4",
    name: "Dr. <PERSON> <PERSON>",
    specialty: "Orthopedics",
    image: "https://images.pexels.com/photos/5327585/pexels-photo-5327585.jpeg?auto=compress&cs=tinysrgb&w=800",
    description: "Dr. Wilson specializes in sports medicine and joint replacement surgeries.",
    education: "MD from Harvard Medical School",
    experience: "18+ years"
  }
];

export const facilities: Facility[] = [
  {
    id: "1",
    name: "Emergency Department",
    description: "24/7 emergency care with state-of-the-art equipment and experienced medical staff.",
    image: "https://images.pexels.com/photos/247786/pexels-photo-247786.jpeg?auto=compress&cs=tinysrgb&w=800"
  },
  {
    id: "2",
    name: "Surgical Center",
    description: "Advanced surgical facilities with the latest technology and specialized surgical teams.",
    image: "https://images.pexels.com/photos/3376790/pexels-photo-3376790.jpeg?auto=compress&cs=tinysrgb&w=800"
  },
  {
    id: "3",
    name: "Diagnostic Imaging",
    description: "Comprehensive imaging services including MRI, CT scans, X-rays, and ultrasound.",
    image: "https://images.pexels.com/photos/4226140/pexels-photo-4226140.jpeg?auto=compress&cs=tinysrgb&w=800"
  },
  {
    id: "4",
    name: "Maternity Ward",
    description: "Comfortable and modern maternity facilities with specialized care for mothers and newborns.",
    image: "https://images.pexels.com/photos/3259629/pexels-photo-3259629.jpeg?auto=compress&cs=tinysrgb&w=800"
  },
  {
    id: "5",
    name: "Rehabilitation Center",
    description: "Comprehensive rehabilitation services for physical therapy, occupational therapy, and more.",
    image: "https://images.pexels.com/photos/7089401/pexels-photo-7089401.jpeg?auto=compress&cs=tinysrgb&w=800"
  },
  {
    id: "6",
    name: "Laboratory Services",
    description: "Advanced laboratory facilities for accurate and timely diagnostic testing.",
    image: "https://images.pexels.com/photos/8376331/pexels-photo-8376331.jpeg?auto=compress&cs=tinysrgb&w=800"
  }
];

export const services: Service[] = [
  {
    id: "1",
    name: "Primary Care",
    description: "Comprehensive healthcare services for individuals and families.",
    icon: "stethoscope"
  },
  {
    id: "2",
    name: "Specialized Treatment",
    description: "Advanced care for specific conditions from our specialists.",
    icon: "heart-pulse"
  },
  {
    id: "3",
    name: "Preventive Care",
    description: "Regular check-ups and screenings to maintain your health.",
    icon: "shield-check"
  },
  {
    id: "4",
    name: "Surgical Procedures",
    description: "Both inpatient and outpatient surgical services.",
    icon: "scissors"
  },
  {
    id: "5",
    name: "Telehealth Services",
    description: "Virtual consultations with our healthcare professionals.",
    icon: "video"
  },
  {
    id: "6",
    name: "Mental Health",
    description: "Counseling and therapeutic services for mental wellbeing.",
    icon: "brain"
  }
];