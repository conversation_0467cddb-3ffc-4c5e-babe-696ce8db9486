import React, { useState } from "react";
import { Link } from "react-router-dom";
import { useAuth } from "../context/AuthContext";
import { Menu, X, User, LogOut, Activity } from "lucide-react";

const Navbar: React.FC = () => {
  const { isAuthenticated, logout, currentUser } = useAuth();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <header className="bg-white shadow-md sticky top-0 z-50">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <Link to="/" className="flex items-center space-x-2">
            <Activity className="h-8 w-8 text-blue-700" />
            <span className="text-2xl font-bold text-blue-700">MedCare</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link to="/" className="text-gray-700 hover:text-blue-700 font-medium transition-colors">
              Home
            </Link>
            <Link to="/facilities" className="text-gray-700 hover:text-blue-700 font-medium transition-colors">
              Facilities
            </Link>
            <Link to="/doctors" className="text-gray-700 hover:text-blue-700 font-medium transition-colors">
              Doctors
            </Link>
            <Link to="/appointments" className="text-gray-700 hover:text-blue-700 font-medium transition-colors">
              Appointments
            </Link>
            <Link to="/about" className="text-gray-700 hover:text-blue-700 font-medium transition-colors">
              About
            </Link>
            <Link to="/contact" className="text-gray-700 hover:text-blue-700 font-medium transition-colors">
              Contact
            </Link>
          </nav>

          {/* Auth Buttons */}
          <div className="hidden md:flex items-center space-x-4">
            {isAuthenticated ? (
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <User className="h-5 w-5 text-blue-700" />
                  <span className="text-sm font-medium">{currentUser?.name}</span>
                </div>
                <button
                  onClick={logout}
                  className="flex items-center space-x-1 text-white bg-red-500 hover:bg-red-600 px-4 py-2 rounded-md transition-colors"
                >
                  <LogOut className="h-4 w-4" />
                  <span>Logout</span>
                </button>
              </div>
            ) : (
              <Link
                to="/register"
                className="text-white bg-blue-700 hover:bg-blue-800 px-6 py-2 rounded-md transition-colors"
              >
                Register
              </Link>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button className="md:hidden text-gray-700" onClick={toggleMenu}>
            {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-white border-t border-gray-200">
          <div className="container mx-auto px-4 py-3 space-y-3">
            <Link
              to="/"
              className="block text-gray-700 hover:text-blue-700 font-medium transition-colors"
              onClick={toggleMenu}
            >
              Home
            </Link>
            <Link
              to="/facilities"
              className="block text-gray-700 hover:text-blue-700 font-medium transition-colors"
              onClick={toggleMenu}
            >
              Facilities
            </Link>
            <Link
              to="/doctors"
              className="block text-gray-700 hover:text-blue-700 font-medium transition-colors"
              onClick={toggleMenu}
            >
              Doctors
            </Link>
            <Link
              to="/appointments"
              className="block text-gray-700 hover:text-blue-700 font-medium transition-colors"
              onClick={toggleMenu}
            >
              Appointments
            </Link>
            <Link
              to="/about"
              className="block text-gray-700 hover:text-blue-700 font-medium transition-colors"
              onClick={toggleMenu}
            >
              About
            </Link>
            <Link
              to="/contact"
              className="block text-gray-700 hover:text-blue-700 font-medium transition-colors"
              onClick={toggleMenu}
            >
              Contact
            </Link>
            
            {isAuthenticated ? (
              <div className="pt-2 border-t border-gray-200">
                <div className="flex items-center space-x-2 mb-2">
                  <User className="h-5 w-5 text-blue-700" />
                  <span className="text-sm font-medium">{currentUser?.name}</span>
                </div>
                <button
                  onClick={() => {
                    logout();
                    toggleMenu();
                  }}
                  className="w-full flex items-center justify-center space-x-1 text-white bg-red-500 hover:bg-red-600 px-4 py-2 rounded-md transition-colors"
                >
                  <LogOut className="h-4 w-4" />
                  <span>Logout</span>
                </button>
              </div>
            ) : (
              <Link
                to="/register"
                className="block text-center text-white bg-blue-700 hover:bg-blue-800 px-6 py-2 rounded-md transition-colors"
                onClick={toggleMenu}
              >
                Register
              </Link>
            )}
          </div>
        </div>
      )}
    </header>
  );
};

export default Navbar;