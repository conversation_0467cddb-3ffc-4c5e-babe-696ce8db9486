export interface User {
  id: string;
  name: string;
  email: string;
  registeredAt: Date;
}

export interface Doctor {
  id: string;
  name: string;
  specialty: string;
  image: string;
  description: string;
  education: string;
  experience: string;
}

export interface Facility {
  id: string;
  name: string;
  description: string;
  image: string;
}

export interface Service {
  id: string;
  name: string;
  description: string;
  icon: string;
}

export interface Appointment {
  id: string;
  userId: string;
  doctorId: string;
  date: string;
  time: string;
  reason: string;
  status: 'pending' | 'confirmed' | 'cancelled';
}