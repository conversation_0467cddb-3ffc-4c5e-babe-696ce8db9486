import React from "react";
import { <PERSON> } from "react-router-dom";
import { useAuth } from "../context/AuthContext";
import { Calendar, Plus } from "lucide-react";

const AppointmentsPage: React.FC = () => {
  const { isAuthenticated, currentUser } = useAuth();

  if (!isAuthenticated) {
    return (
      <div className="py-12 bg-gray-100 min-h-screen">
        <div className="container mx-auto px-4">
          <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-8 text-center">
            <h2 className="text-2xl font-bold text-gray-800 mb-4">Login Required</h2>
            <p className="text-gray-600 mb-6">
              Please register or log in to view and manage your appointments.
            </p>
            <Link
              to="/register"
              className="inline-block bg-blue-600 hover:bg-blue-700 text-white py-2 px-6 rounded-md font-medium transition-colors"
            >
              Register Now
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // This would normally be fetched from an API
  const mockAppointments = [
    {
      id: "1",
      doctorName: "Dr. <PERSON>",
      specialty: "Cardiology",
      date: "2025-05-15",
      time: "10:00 AM",
      status: "confirmed"
    },
    {
      id: "2",
      doctorName: "Dr. Michael Chen",
      specialty: "Neurology",
      date: "2025-05-20",
      time: "2:30 PM",
      status: "pending"
    }
  ];

  return (
    <div className="py-12 bg-gray-100 min-h-screen">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-800">Your Appointments</h1>
          <Link
            to="/appointments/new"
            className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md font-medium transition-colors"
          >
            <Plus className="h-5 w-5 mr-1" />
            New Appointment
          </Link>
        </div>

        {mockAppointments.length > 0 ? (
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Doctor
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date & Time
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {mockAppointments.map((appointment) => (
                    <tr key={appointment.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="font-medium text-gray-800">{appointment.doctorName}</div>
                        <div className="text-sm text-gray-500">{appointment.specialty}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 text-gray-400 mr-1" />
                          <span>
                            {new Date(appointment.date).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })}
                          </span>
                        </div>
                        <div className="text-sm text-gray-500 ml-5">{appointment.time}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                          ${appointment.status === 'confirmed' 
                            ? 'bg-green-100 text-green-800' 
                            : appointment.status === 'pending' 
                              ? 'bg-yellow-100 text-yellow-800' 
                              : 'bg-red-100 text-red-800'}`}
                        >
                          {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <a href="#" className="text-blue-600 hover:text-blue-800 mr-3">
                          Details
                        </a>
                        {appointment.status !== 'cancelled' && (
                          <a href="#" className="text-red-600 hover:text-red-800">
                            Cancel
                          </a>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-md p-8 text-center">
            <div className="mb-4">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto" />
            </div>
            <h3 className="text-xl font-medium text-gray-800 mb-2">No Appointments Yet</h3>
            <p className="text-gray-600 mb-6">
              You don't have any appointments scheduled. Book your first appointment now.
            </p>
            <Link
              to="/appointments/new"
              className="inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white py-2 px-6 rounded-md font-medium transition-colors"
            >
              <Plus className="h-5 w-5 mr-1" />
              Book Appointment
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};

export default AppointmentsPage;