import React from "react";
import { <PERSON>ethos<PERSON>, HeartPulse, ShieldCheck, Scissors, Video, Brain, DivideIcon as LucideIcon } from "lucide-react";

interface ServiceCardProps {
  name: string;
  description: string;
  icon: string;
}

const ServiceCard: React.FC<ServiceCardProps> = ({ name, description, icon }) => {
  const getIcon = (): JSX.Element => {
    const iconProps = { className: "h-10 w-10 text-blue-600 mb-3" };
    
    switch (icon) {
      case "stethoscope":
        return <Stethoscope {...iconProps} />;
      case "heart-pulse":
        return <HeartPulse {...iconProps} />;
      case "shield-check":
        return <ShieldCheck {...iconProps} />;
      case "scissors":
        return <Scissors {...iconProps} />;
      case "video":
        return <Video {...iconProps} />;
      case "brain":
        return <Brain {...iconProps} />;
      default:
        return <Stethoscope {...iconProps} />;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6 text-center transition-transform duration-300 hover:shadow-xl hover:-translate-y-1">
      <div className="flex justify-center">
        {getIcon()}
      </div>
      <h3 className="text-xl font-bold text-gray-800 mb-2">{name}</h3>
      <p className="text-gray-600">{description}</p>
    </div>
  );
};

export default ServiceCard;