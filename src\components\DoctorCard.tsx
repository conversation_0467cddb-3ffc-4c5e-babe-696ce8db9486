import React from "react";
import { Link } from "react-router-dom";

interface DoctorCardProps {
  id: string;
  name: string;
  specialty: string;
  image: string;
  description: string;
}

const DoctorCard: React.FC<DoctorCardProps> = ({
  id,
  name,
  specialty,
  image,
  description
}) => {
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden transition-transform duration-300 hover:shadow-xl">
      <div className="h-64 overflow-hidden">
        <img
          src={image}
          alt={name}
          className="w-full h-full object-cover"
        />
      </div>
      <div className="p-6">
        <h3 className="text-xl font-bold text-gray-800 mb-1">{name}</h3>
        <p className="text-blue-600 font-medium mb-3">{specialty}</p>
        <p className="text-gray-600 mb-4 line-clamp-3">{description}</p>
        <div className="flex space-x-3">
          <Link
            to={`/doctors/${id}`}
            className="flex-1 text-center bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors"
          >
            View Profile
          </Link>
          <Link
            to={`/appointments/new?doctorId=${id}`}
            className="flex-1 text-center border border-blue-600 text-blue-600 hover:bg-blue-50 py-2 px-4 rounded-md transition-colors"
          >
            Book Appointment
          </Link>
        </div>
      </div>
    </div>
  );
};

export default DoctorCard;