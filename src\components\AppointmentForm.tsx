import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "../context/AuthContext";
import { doctors } from "../data/mockData";
import { Calendar, Clock } from "lucide-react";

const AppointmentForm: React.FC = () => {
  const { isAuthenticated, currentUser } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const preselectedDoctorId = queryParams.get("doctorId");

  const [formData, setFormData] = useState({
    doctorId: preselectedDoctorId || "",
    date: "",
    time: "",
    reason: ""
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  useEffect(() => {
    if (!isAuthenticated) {
      navigate("/register", { state: { from: location } });
    }
  }, [isAuthenticated, navigate, location]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    let isValid = true;

    if (!formData.doctorId) {
      newErrors.doctorId = "Please select a doctor";
      isValid = false;
    }

    if (!formData.date) {
      newErrors.date = "Please select a date";
      isValid = false;
    }

    if (!formData.time) {
      newErrors.time = "Please select a time";
      isValid = false;
    }

    if (!formData.reason.trim()) {
      newErrors.reason = "Please provide a reason for your appointment";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      setIsSubmitting(true);
      
      // Simulate API call
      setTimeout(() => {
        setIsSubmitting(false);
        setSubmitted(true);
        
        // Redirect after showing success message
        setTimeout(() => {
          navigate("/appointments");
        }, 2000);
      }, 1500);
    }
  };

  // Generate available time slots
  const timeSlots = [
    "9:00 AM", "9:30 AM", "10:00 AM", "10:30 AM", 
    "11:00 AM", "11:30 AM", "1:00 PM", "1:30 PM",
    "2:00 PM", "2:30 PM", "3:00 PM", "3:30 PM",
    "4:00 PM", "4:30 PM"
  ];

  // Get tomorrow's date as min date for the date picker
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  const minDate = tomorrow.toISOString().split("T")[0];

  // Get date 3 months from now as max date
  const maxDate = new Date();
  maxDate.setMonth(maxDate.getMonth() + 3);
  const maxDateStr = maxDate.toISOString().split("T")[0];

  if (submitted) {
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
        <svg className="w-16 h-16 text-green-500 mx-auto mb-4\" fill="none\" stroke="currentColor\" viewBox="0 0 24 24\" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round\" strokeLinejoin="round\" strokeWidth="2\" d="M5 13l4 4L19 7"></path>
        </svg>
        <h3 className="text-xl font-bold text-green-800 mb-2">Appointment Scheduled!</h3>
        <p className="text-green-700 mb-4">
          Your appointment has been successfully scheduled. You will receive a confirmation email shortly.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">Schedule an Appointment</h2>
      
      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <label htmlFor="doctorId" className="block text-gray-700 font-medium mb-2">
            Select Doctor
          </label>
          <select
            id="doctorId"
            name="doctorId"
            value={formData.doctorId}
            onChange={handleChange}
            className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 ${
              errors.doctorId ? "border-red-500 focus:ring-red-200" : "border-gray-300 focus:ring-blue-200"
            }`}
          >
            <option value="">Select a doctor</option>
            {doctors.map(doctor => (
              <option key={doctor.id} value={doctor.id}>
                {doctor.name} - {doctor.specialty}
              </option>
            ))}
          </select>
          {errors.doctorId && <p className="mt-1 text-red-500 text-sm">{errors.doctorId}</p>}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label htmlFor="date" className="block text-gray-700 font-medium mb-2">
              Select Date
            </label>
            <div className="relative">
              <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <input
                type="date"
                id="date"
                name="date"
                value={formData.date}
                onChange={handleChange}
                min={minDate}
                max={maxDateStr}
                className={`w-full pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                  errors.date ? "border-red-500 focus:ring-red-200" : "border-gray-300 focus:ring-blue-200"
                }`}
              />
            </div>
            {errors.date && <p className="mt-1 text-red-500 text-sm">{errors.date}</p>}
          </div>

          <div>
            <label htmlFor="time" className="block text-gray-700 font-medium mb-2">
              Select Time
            </label>
            <div className="relative">
              <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <select
                id="time"
                name="time"
                value={formData.time}
                onChange={handleChange}
                className={`w-full pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                  errors.time ? "border-red-500 focus:ring-red-200" : "border-gray-300 focus:ring-blue-200"
                }`}
              >
                <option value="">Select a time</option>
                {timeSlots.map(time => (
                  <option key={time} value={time}>
                    {time}
                  </option>
                ))}
              </select>
            </div>
            {errors.time && <p className="mt-1 text-red-500 text-sm">{errors.time}</p>}
          </div>
        </div>

        <div className="mb-6">
          <label htmlFor="reason" className="block text-gray-700 font-medium mb-2">
            Reason for Visit
          </label>
          <textarea
            id="reason"
            name="reason"
            value={formData.reason}
            onChange={handleChange}
            rows={4}
            className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 ${
              errors.reason ? "border-red-500 focus:ring-red-200" : "border-gray-300 focus:ring-blue-200"
            }`}
            placeholder="Please describe your symptoms or reason for the appointment"
          ></textarea>
          {errors.reason && <p className="mt-1 text-red-500 text-sm">{errors.reason}</p>}
        </div>

        <button
          type="submit"
          disabled={isSubmitting}
          className={`w-full bg-blue-600 text-white font-medium py-3 px-4 rounded-md ${
            isSubmitting ? "opacity-70 cursor-not-allowed" : "hover:bg-blue-700"
          } transition-colors`}
        >
          {isSubmitting ? "Scheduling..." : "Schedule Appointment"}
        </button>
      </form>
    </div>
  );
};

export default AppointmentForm;