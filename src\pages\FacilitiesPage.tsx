import React from "react";
import FacilityCard from "../components/FacilityCard";
import { facilities } from "../data/mockData";

const FacilitiesPage: React.FC = () => {
  return (
    <div className="py-12 bg-gray-100">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">Our Facilities</h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            At MedCare Hospital, we provide state-of-the-art facilities equipped with the latest technology and staffed by highly trained professionals to ensure the best care for our patients.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {facilities.map((facility) => (
            <FacilityCard
              key={facility.id}
              name={facility.name}
              description={facility.description}
              image={facility.image}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default FacilitiesPage;